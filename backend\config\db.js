import mongoose from "mongoose"; // Import the mongoose module.

const connectDB = async () => { // Create an asynchronous function to connect to MongoDB.
    try {
        const conn = await mongoose.connect(process.env.MONGO_URI); // Connect to MongoDB using the MONGO_URI environment variable.
        console.log(`MongoDB Connected: ${conn.connection.host}`); // Log the connection details.
    } catch (error) {
        console.error(error); // Log the error.
        process.exit(1); // Exit the process with an error code.
    }
};

export default connectDB; // Export the connectDB function.