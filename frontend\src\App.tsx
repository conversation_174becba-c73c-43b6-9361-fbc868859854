import { Routes, Route } from "react-router";
import { HomePage } from "./pages/HomePage";
import { CreatePage } from "./pages/CreatePage";
import {Box} from "@chakra-ui/react";
import {Navbar} from "@/components/Navbar.tsx";
import {useColorModeValue} from "@/components/ui/color-mode.tsx";

function App() {
  return (
      <>
        <Box minH={"100vh"} bg={useColorModeValue("gray.100", "gray.900")}>
            <Navbar />
            <Routes>
                <Route path="/" element={<HomePage/>}/>
                <Route path="/create" element={<CreatePage/>}/>
            </Routes>
        </Box>
      </>
  );
}

export default App;
