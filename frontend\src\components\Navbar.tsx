import {Button, Container, Flex, H<PERSON>tack, StackSeparator, Text} from "@chakra-ui/react";
import {Link} from "react-router"
import {BiSolidMoon, BiSolidPlusSquare, BiSolidSun} from "react-icons/bi";
import {useColorMode, useColorModeValue} from "@/components/ui/color-mode.tsx";

export const Navbar = () => {
    const { toggleColorMode } = useColorMode()
    const colorModeIcon = useColorModeValue(<BiSolidSun />, <BiSolidMoon />)

    return(
        <Container maxW={"container.xl"}>
            <Flex
                h={16}
                alignItems={"center"}
                justifyContent={"space-between"}
            >
                <Link to={"/"}>
                    <Text
                        bgGradient="to-r" gradientFrom="#2196F3" gradientTo="#4FC3F7"
                        bgClip='text'
                        fontSize={{base: "2xl", md: "3xl"}}
                        fontWeight='extrabold'
                    >
                        🛒 Product Store
                    </Text>
                </Link>
                <HStack
                    alignItems={"center"}
                    separator={<StackSeparator />}
                >
                    <Link to={"/create"}>
                    <Button
                    >
                        <BiSolidPlusSquare />
                    </Button>
                    </Link>
                    <Button
                        onClick={toggleColorMode}
                    >
                        {colorModeIcon}
                    </Button>
                </HStack>
            </Flex>
        </Container>
    )
}