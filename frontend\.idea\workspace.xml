<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9795903a-f258-41cd-a6c6-abb5db8dae18" name="Changes" comment="✨ feat(frontend): add homepage, navbar, and create product page&#10;&#10;- create `HomePage` component for the main landing page&#10;- add `Navbar` component with navigation links and color mode toggle&#10;- create `CreatePage` component with input form for adding products&#10;- update `App` component to include routes for Home and Create pages" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="TypeScript JSX File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zoWpQcqgUM83ZBYBWB1Jdb45UV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JavaScript Debug.localhost:5173.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Node.js.Unnamed.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "F:/Projects/WebDev/Javascript/mern/product-store/frontend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Run",
    "settings.editor.selected.configurable": "editor.preferences.inline.completion",
    "ts.external.directory.path": "F:\\Projects\\WebDev\\Javascript\\mern\\product-store\\frontend\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9795903a-f258-41cd-a6c6-abb5db8dae18" name="Changes" comment="" />
      <created>*************</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>*************</updated>
      <workItem from="*************" duration="1735000" />
      <workItem from="*************" duration="45000" />
      <workItem from="*************" duration="********" />
    </task>
    <task id="LOCAL-00001" summary="✨ feat(frontend): integrate react-router and setup path aliases&#10;&#10;- add react-router for routing management&#10;- wrap App with BrowserRouter&#10;- configure path aliases with `@` for cleaner imports&#10;- update tsconfig and vite config for alias support&#10;- remove unused `react.svg` asset">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00002" summary="✨ feat(frontend): add homepage, navbar, and create product page&#10;&#10;- create `HomePage` component for the main landing page&#10;- add `Navbar` component with navigation links and color mode toggle&#10;- create `CreatePage` component with input form for adding products&#10;- update `App` component to include routes for Home and Create pages">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="✨ feat(frontend): integrate react-router and setup path aliases&#10;&#10;- add react-router for routing management&#10;- wrap App with BrowserRouter&#10;- configure path aliases with `@` for cleaner imports&#10;- update tsconfig and vite config for alias support&#10;- remove unused `react.svg` asset" />
    <MESSAGE value="✨ feat(frontend): add homepage, navbar, and create product page&#10;&#10;- create `HomePage` component for the main landing page&#10;- add `Navbar` component with navigation links and color mode toggle&#10;- create `CreatePage` component with input form for adding products&#10;- update `App` component to include routes for Home and Create pages" />
    <option name="LAST_COMMIT_MESSAGE" value="✨ feat(frontend): add homepage, navbar, and create product page&#10;&#10;- create `HomePage` component for the main landing page&#10;- add `Navbar` component with navigation links and color mode toggle&#10;- create `CreatePage` component with input form for adding products&#10;- update `App` component to include routes for Home and Create pages" />
  </component>
</project>