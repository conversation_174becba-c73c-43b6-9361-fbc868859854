import Product from "../models/product.model.js";
import mongoose from "mongoose";

// Get all products
export const getProducts = async (req, res) => {
    try {
        const products = await Product.find({});
        res.status(200).json({ success: true, data: products });
    } catch (error) {
        console.error(`Error fetching products: ${error}`);
        res.status(500).json({ 
            success: false, 
            message: "Products fetch failed", 
            error: error.message 
        });
    }
};

// Create a new product
export const createProduct = async (req, res) => {
    const product = req.body;

    if (!product.name || !product.price || !product.image) {
        return res.status(400).json({ 
            success: false, 
            message: "Please provide all the required fields" 
        });
    }

    const newProduct = new Product(product);

    try {
        await newProduct.save();
        res.status(201).json({ 
            success: true, 
            message: "Product created successfully", 
            data: newProduct 
        });
    } catch (error) {
        console.error(`Error creating product: ${error}`);
        res.status(500).json({ 
            success: false, 
            message: "Product creation failed", 
            error: error.message 
        });
    }
};

// Update a product
export const updateProduct = async (req, res) => {
    const { id } = req.params;
    const product = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.status(400).json({ 
            success: false, 
            message: "Invalid product id" 
        });
    }

    if (!product.name || !product.price || !product.image) {
        const missingFields = [];
        if (!product.name) missingFields.push('name');
        if (!product.price) missingFields.push('price');
        if (!product.image) missingFields.push('image');
        
        return res.status(400).json({ 
            success: false, 
            message: `Please provide all the required fields: ${missingFields.join(', ')}` 
        });
    }

    try {
        const updatedProduct = await Product.findByIdAndUpdate(id, product, { new: true });
        if (!updatedProduct) {
            return res.status(404).json({ 
                success: false, 
                message: "Product not found" 
            });
        }
        res.status(200).json({ 
            success: true, 
            message: "Product updated successfully", 
            data: updatedProduct 
        });
    } catch (error) {
        console.error(`Error updating product: ${error}`);
        res.status(500).json({ 
            success: false, 
            message: "Product update failed", 
            error: error.message 
        });
    }
};

// Delete a product
export const deleteProduct = async (req, res) => {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.status(400).json({ 
            success: false, 
            message: "Invalid product id" 
        });
    }

    try {
        const deletedProduct = await Product.findByIdAndDelete(id);
        if (!deletedProduct) {
            return res.status(404).json({ 
                success: false, 
                message: "Product not found" 
            });
        }
        res.status(200).json({ 
            success: true, 
            message: "Product deleted successfully" 
        });
    } catch (error) {
        console.error(`Error deleting product: ${error}`);
        res.status(500).json({ 
            success: false, 
            message: "Product deletion failed", 
            error: error.message 
        });
    }
};
