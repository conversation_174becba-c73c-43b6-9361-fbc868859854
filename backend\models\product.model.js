import mongoose from "mongoose"; // Import the mongoose module.

const productSchema = new mongoose.Schema({ // Create a new schema for products.
    name: { type: String, required: true }, // Name is a required string.
    price: { type: Number, required: true }, // Price is a required number.
    image: { type: String, required: true }, // Image is a required string.
}, { timestamps: true });

const Product = mongoose.model("Product", productSchema); // Create a new model for products.

export default Product; // Export the Product model.