import { Box, Input, Text, But<PERSON>, For, HStack } from "@chakra-ui/react";
import { useColorModeValue } from "@/components/ui/color-mode.tsx";
import { useState, type ChangeEvent, type FormEvent } from "react";
import { useProductStore } from "@/store/product";
import { toaster } from "@/components/ui/toaster";

export const CreatePage = () => {
  const [newProduct, setNewProduct] = useState<{
    name: string;
    price: number;
    imageUrl: string;
  }>({
    name: "",
    price: 0,
    imageUrl: "",
  });

  const { createProduct } = useProductStore();

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewProduct((prev) => ({
      ...prev,
      [name]: name === "price" ? Number(value) : value,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    toaster.create({
      title: "Creating Product",
      description: "Your product is being created.",
      type: "loading",
    });
    
    const { success, message } = await createProduct(newProduct); // Call the createProduct function with the newProduct object
    toaster.dismiss()
    if (success) {
      toaster.create({
        title: "Product Created",
        description: "Your product has been successfully created.",
        type: "success",
      })
      setNewProduct({
        name: "",
        price: 0,
        imageUrl: "",
      });
    } else {
      // Handle error, e.g., show an error message
      toaster.create({
        title: "Error",
        description: "Failed to create product.",
        type: "error",
      })
      console.error(message);
    }
  };

  return (
    <>
      <Text
        fontSize={{ base: "4xl", md: "5xl" }}
        fontWeight="bold"
        textAlign="center"
        mt={10}
      >
        Create Your Product
      </Text>

      <Box
        as="form"
        onSubmit={handleSubmit}
        mx="auto"
        maxW="500px"
        mt={8}
        p={6}
        bg={useColorModeValue("gray.200", "gray.800")}
        borderRadius="lg"
        boxShadow="base"
      >
        <Input
          bg={useColorModeValue("white", "black")}
          name="name"
          value={newProduct.name}
          onChange={handleInputChange}
          placeholder="Product Name"
          mb={4}
        />
        <Input
          bg={useColorModeValue("white", "black")}
          name="price"
          value={newProduct.price}
          onChange={handleInputChange}
          placeholder="Price"
          type="number"
          mb={4}
        />
        <Input
          bg={useColorModeValue("white", "black")}
          name="imageUrl"
          value={newProduct.imageUrl}
          onChange={handleInputChange}
          placeholder="Image URL"
          mb={4}
        />
        <HStack>
      <For each={["success", "error", "warning", "info"]}>
        {(type) => (
          <Button
            size="sm"
            variant="outline"
            key={type}
            onClick={() =>
              toaster.create({
                title: `Toast status is ${type}`,
                type: type,
              })
            }
          >
            {type}
          </Button>
        )}
      </For>
    </HStack>
        <Button w="100%" colorScheme="" size="lg" type="submit">
          Create Product
        </Button>
      </Box>
    </>
  );
};
