import { create } from "zustand";

// Product interface based on the backend model
export interface Product {
  name: string;
  price: number;
  imageUrl: string;
}

// Store interface
interface ProductStore {
  products: Product[];
  setProducts: (products: Product[]) => void;
  createProduct: (product: Product) => Promise<{success: boolean, message: string}>;
}

export const useProductStore = create<ProductStore>((set) => ({
  products: [],
  setProducts: (products: Product[]) => set({ products }),
  createProduct: async (product: Product) => {
    if (!product.name || !product.price || !product.imageUrl) {
        return {success: false, message: "Please provide all the required fields" };
    }

    try {
        const response = await fetch("http://localhost:5000/api/products", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(product)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        set((state) => ({ products: [...state.products, data.data] }));
        return {success: true, message: "Product created successfully" };
    } catch (error) {
        console.error(`Error creating product: ${error}`);
        return {success: false, message: "Product creation failed" };
    }
  }
}));
